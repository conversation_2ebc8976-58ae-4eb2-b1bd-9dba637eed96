import { CoreDB, CoreDBUser } from "../../../../back/src/core/coreDB/CoreDB"


export interface IBackendBaseNodeContext {
    db: CoreDB
    global: any
    flow: any
    type: any
    node: { id: string }
}

export interface ISetupContext {
    global: any
    flow: any
    type: any
    node: any | { id: string }
}

export class NodeBackendBaseV1 {
    private db: any
    private dbUser: CoreDBUser
    public context: ISetupContext

    constructor(context: IBackendBaseNodeContext) {
        this.db = context.db
        this.context = context as ISetupContext
        delete this.context.db   // Don't expose db directly.
        this.dbUser = new CoreDBUser(this.db)
    }

    on = (propName: string, cb: (val: any) => void) => {
        this.dbUser.on(this.context.node.id + "." + propName, cb)
    }

    ins = {
        on: (inputName: string, cb: (val: any) => void) => {
            this.dbUser.on(this.context.node.id + ".ins." + inputName, cb)
        }
    }

    outs = {
        set: (outputName: string, value: any) => {
            this.dbUser.patch(this.context.node.id + ".outs." + outputName, value)
        }
    }

    // calls = {
    //     on: (callName: string, ...args: any[]) => { }
    // }
    // call = (callName: string, ...args: any[]) => { }

    _baseSetup() {
        console.log("Setting up NodeBackendBaseV1:", this.context)
        this.setup()
    }
    _baseCleanup() {
        console.log("Tearing down NodeBackendBaseV1:", this.context)
        this.cleanup()
        this.dbUser.unsubscribeAll()
    }

    setup() {
        // Implementation for setup
        console.log("setup function for node missing override.", this.context)
    }

    cleanup() {
        // Implementation for takeDown
        console.log("cleanup function for node missing override.", this.context)
    }
}

